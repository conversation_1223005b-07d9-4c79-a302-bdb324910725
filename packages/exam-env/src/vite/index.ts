import type { UserConfig, ConfigEnv } from 'vite';
import { mergeConfig } from 'vite';
import type { AppType, ViteConfigOptions } from './types.js';
import { getBaseConfig } from './base.js';
import { getAdminConfig } from '../app/admin.js';
import { getUserConfig } from '../app/user.js';
import { createAliasConfig } from './alias.js';

/**
 * 创建 Vite 配置的工厂函数
 * @param options 配置选项
 * @returns Vite 配置
 */
export function createViteConfig(options: ViteConfigOptions): UserConfig {
    const { appType, configEnv, cwd = process.cwd(), aliasConfig = [], customConfig = {} } = options;
    const { mode } = configEnv;

    // 获取基础配置
    const baseConfig = getBaseConfig(cwd, mode, aliasConfig);

    // 根据应用类型获取特定配置
    let appConfig: UserConfig = {};
    switch (appType) {
        case 'admin':
            appConfig = getAdminConfig(cwd, mode);
            break;
        case 'user':
            appConfig = getUserConfig(cwd, mode);
            break;
        default:
            throw new Error(`Unsupported app type: ${appType}`);
    }

    // 合并配置：基础配置 + 应用特定配置 + 自定义配置
    const finalConfig = mergeConfig(mergeConfig(baseConfig, appConfig), customConfig);

    return finalConfig;
}

/**
 * 快速创建配置的辅助函数
 * @param appType 应用类型
 * @param customConfig 自定义配置
 * @returns 返回一个接受 ConfigEnv 的函数
 */
export function defineViteConfig(appType: AppType, customConfig?: UserConfig) {
    return (configEnv: ConfigEnv): UserConfig => {
        const cwd = process.cwd();
        return createViteConfig({
            appType,
            configEnv,
            cwd,
            aliasConfig: createAliasConfig(cwd),
            customConfig,
        });
    };
}

// 导出类型
export type * from './types.js';

// 导出配置函数
export { getBaseConfig } from './base.js';
export { getAdminConfig } from '../app/admin.js';
export { getUserConfig } from '../app/user.js';
export { createAliasConfig } from './alias.js';
